/**
 * Test script to verify POS Square payment integration in production
 * This script can be run in the browser console to test the payment form
 */

console.log('🧪 Testing POS Square Payment Integration in Production...')

// Test 1: Check if the page loads without errors
console.log('✅ Test 1: Page loaded successfully')

// Test 2: Check if Square SDK loads
const checkSquareSDK = () => {
  if (window.Square) {
    console.log('✅ Test 2: Square SDK loaded successfully')
    return true
  } else {
    console.log('❌ Test 2: Square SDK not loaded')
    return false
  }
}

// Test 3: Check if payment form container exists
const checkContainer = () => {
  const container = document.querySelector('[data-testid="square-card-container"]') || 
                   document.getElementById('pos-square-card-container') ||
                   document.querySelector('.cardForm')
  
  if (container) {
    console.log('✅ Test 3: Payment form container found:', container)
    return container
  } else {
    console.log('❌ Test 3: Payment form container not found')
    return null
  }
}

// Test 4: Check for DOM manipulation errors
const checkForDOMErrors = () => {
  const originalError = console.error
  let domErrors = []
  
  console.error = (...args) => {
    const errorMsg = args.join(' ')
    if (errorMsg.includes('removeChild') || 
        errorMsg.includes('Node') || 
        errorMsg.includes('DOM')) {
      domErrors.push(errorMsg)
    }
    originalError.apply(console, args)
  }
  
  // Restore original console.error after 5 seconds
  setTimeout(() => {
    console.error = originalError
    if (domErrors.length === 0) {
      console.log('✅ Test 4: No DOM manipulation errors detected')
    } else {
      console.log('❌ Test 4: DOM manipulation errors detected:', domErrors)
    }
  }, 5000)
}

// Test 5: Try to trigger Square form initialization
const testSquareFormInit = () => {
  if (window.Square && checkContainer()) {
    console.log('🔄 Test 5: Attempting Square form initialization...')
    
    // Trigger force initialization event
    const event = new CustomEvent('forceSquareInit', {
      detail: { source: 'production-test' }
    })
    window.dispatchEvent(event)
    
    setTimeout(() => {
      const container = checkContainer()
      if (container && container.children.length > 0) {
        console.log('✅ Test 5: Square form appears to have initialized')
      } else {
        console.log('⚠️ Test 5: Square form initialization unclear')
      }
    }, 3000)
  } else {
    console.log('❌ Test 5: Cannot test Square form - SDK or container missing')
  }
}

// Run all tests
setTimeout(() => {
  console.log('🚀 Starting POS production tests...')
  
  checkForDOMErrors()
  
  setTimeout(() => {
    checkSquareSDK()
    checkContainer()
    testSquareFormInit()
  }, 1000)
}, 1000)

console.log('📋 Test script loaded. Tests will run automatically.')
console.log('💡 You can also run individual tests manually:')
console.log('   - checkSquareSDK()')
console.log('   - checkContainer()')
console.log('   - testSquareFormInit()')
