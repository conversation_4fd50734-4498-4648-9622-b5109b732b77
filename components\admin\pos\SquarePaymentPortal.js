import { useEffect, useRef } from 'react'

const SquarePaymentPortal = ({
  containerElement,
  onFormReady,
  onFormError,
  isProduction,
  appId,
  locationId
}) => {
  const squareFormRef = useRef(null)
  const mountedRef = useRef(true)

  useEffect(() => {
    console.log('🔄 SquarePaymentPortal initializing...')
    
    const initializeSquareForm = async () => {
      if (!window.Square || !containerElement || !appId || !locationId) {
        console.error('❌ Missing Square dependencies:', {
          square: !!window.Square,
          container: !!containerElement,
          appId: !!appId,
          locationId: !!locationId
        })
        onFormError(new Error('Missing Square SDK or configuration'))
        return
      }

      try {
        console.log('🔄 Initializing Square form in portal...')
        
        // Clear container
        containerElement.innerHTML = ''
        
        // Initialize Square payments
        const payments = window.Square.payments(appId, locationId)
        
        // Configure card options
        const cardOptions = {
          style: {
            '.input-container': {
              borderColor: '#e0e0e0',
              borderRadius: '8px',
              padding: '12px'
            },
            '.input-container.is-focus': {
              borderColor: '#4ECDC4'
            },
            '.input-container.is-error': {
              borderColor: '#dc3545'
            },
            '.message-text': {
              color: '#dc3545'
            }
          }
        }

        // Create and attach card form
        const card = await payments.card(cardOptions)
        await card.attach(containerElement)
        
        squareFormRef.current = card
        
        console.log('✅ Square form initialized successfully in portal')
        onFormReady(card)
        
      } catch (error) {
        console.error('❌ Square form initialization failed:', error)
        onFormError(error)
      }
    }

    // Initialize form
    initializeSquareForm()

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up Square form in portal...')
      mountedRef.current = false
      
      if (squareFormRef.current) {
        try {
          squareFormRef.current.destroy()
          console.log('✅ Square form cleaned up successfully')
        } catch (error) {
          console.warn('⚠️ Error cleaning up Square form:', error)
        }
      }
    }
  }, [containerElement, appId, locationId, onFormReady, onFormError])

  // This component doesn't render anything directly
  // The Square form is rendered into the containerElement
  return null
}

export default SquarePaymentPortal
