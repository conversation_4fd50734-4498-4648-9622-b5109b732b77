import { useState, useEffect, useRef, useCallback } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSSquarePayment component for processing Square card payments in POS
 *
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
export default function POSSquarePayment({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [paymentForm, setPaymentForm] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [squareSDKLoaded, setSquareSDKLoaded] = useState(false)
  const [billingAddress, setBillingAddress] = useState({
    addressLine1: '1455 Market St',
    addressLine2: 'Suite 600',
    locality: 'San Francisco',
    administrativeDistrictLevel1: 'CA',
    postalCode: '94103',
    country: 'US'
  })
  const [showBillingAddress, setShowBillingAddress] = useState(false)


  const containerRef = useRef(null)
  const mountedRef = useRef(true)
  const sdkLoadingRef = useRef(false)

  // Initialize Square form directly in the React container
  const initializeSquareForm = useCallback(async () => {
    if (!squareSDKLoaded || !containerRef.current || paymentForm || !mountedRef.current) {
      return
    }

    console.log('🔄 Initializing Square form directly in React container...')

    try {
      const container = containerRef.current

      // Clear container safely
      if (container.firstChild) {
        container.textContent = ''
      }

      const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
      const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

      if (!appId || !locationId) {
        throw new Error('Missing Square configuration')
      }

      const payments = window.Square.payments(appId, locationId)

      const cardOptions = {
        style: {
          '.input-container': {
            borderColor: '#e0e0e0',
            borderRadius: '8px'
          },
          '.input-container.is-focus': {
            borderColor: '#4ECDC4'
          },
          '.input-container.is-error': {
            borderColor: '#dc3545'
          }
        }
      }

      const card = await payments.card(cardOptions)
      await card.attach(container)

      setPaymentForm(card)
      setIsLoading(false)
      setErrorMessage('')

      console.log('✅ Square form initialized successfully')
    } catch (error) {
      console.error('❌ Square form initialization failed:', error)
      setIsLoading(false)
      setErrorMessage('Failed to initialize payment form. Please try again.')
      onError(error)
    }
  }, [squareSDKLoaded, paymentForm, onError])



  // Load Square SDK and initialize isolated container
  useEffect(() => {
    console.log('🛡️ POSSquarePayment component mounting...')

    const loadSquareSDK = async () => {
      if (sdkLoadingRef.current || window.Square) {
        if (window.Square) {
          setSquareSDKLoaded(true)
        }
        return
      }

      sdkLoadingRef.current = true
      console.log('🔄 Loading Square SDK...')

      try {
        // Check if script already exists
        let script = document.querySelector('script[src*="square.js"]')

        if (!script) {
          script = document.createElement('script')
          script.src = 'https://sandbox.web.squarecdn.com/v1/square.js'
          script.async = true
          document.head.appendChild(script)
        }

        // Wait for Square SDK to load
        await new Promise((resolve, reject) => {
          if (window.Square) {
            resolve()
            return
          }

          script.onload = () => {
            if (window.Square) {
              console.log('✅ Square SDK loaded successfully')
              resolve()
            } else {
              reject(new Error('Square SDK loaded but not available'))
            }
          }

          script.onerror = () => {
            reject(new Error('Failed to load Square SDK'))
          }

          // Timeout after 10 seconds
          setTimeout(() => {
            reject(new Error('Square SDK load timeout'))
          }, 10000)
        })

        setSquareSDKLoaded(true)
      } catch (error) {
        console.error('❌ Failed to load Square SDK:', error)
        setIsLoading(false)
        setErrorMessage('Failed to load payment system. Please refresh the page.')
        onError(error)
      }
    }

    loadSquareSDK()

    return () => {
      console.log('🛡️ POSSquarePayment component unmounting...')
      mountedRef.current = false

      // Clean up Square form
      if (paymentForm) {
        try {
          paymentForm.destroy()
          console.log('✅ Square form destroyed')
        } catch (error) {
          console.warn('⚠️ Error destroying Square form:', error)
        }
      }
    }
  }, [])

  // Initialize Square form when SDK is loaded
  useEffect(() => {
    if (squareSDKLoaded && !paymentForm && mountedRef.current) {
      console.log('🔄 Initializing Square form...')

      // Determine environment
      const isSandbox = process.env.NODE_ENV !== 'production' ||
                       process.env.SQUARE_ENVIRONMENT === 'sandbox'

      if (isSandbox) {
        setShowBillingAddress(true)
      }

      // Initialize the form
      initializeSquareForm()
    }
  }, [squareSDKLoaded, paymentForm, initializeSquareForm])

  // Listen for force initialization events from debugger
  useEffect(() => {
    const handleForceInit = (event) => {
      console.log('🔄 Force initialization triggered by:', event.detail?.source)
      if (mountedRef.current && window.Square) {
        setIsLoading(true)
        setErrorMessage('')

        // Clean up existing form
        if (paymentForm) {
          try {
            paymentForm.destroy()
          } catch (error) {
            console.warn('Error destroying existing form:', error)
          }
        }
        setPaymentForm(null)

        // Reinitialize
        setTimeout(() => {
          if (mountedRef.current) {
            initializeSquareForm()
          }
        }, 100)
      }
    }

    window.addEventListener('forceSquareInit', handleForceInit)
    return () => window.removeEventListener('forceSquareInit', handleForceInit)
  }, [paymentForm, initializeSquareForm])

  // Simplified payment processing with isolated container
  const handlePayment = useCallback(async () => {
    if (!paymentForm || isProcessing || !mountedRef.current) {
      console.warn('Payment form not ready or already processing')
      return
    }

    console.log('🔄 Processing payment with isolated Square form...')
    setIsProcessing(true)
    setErrorMessage('')

    try {
      // Enable payment operation protection
      const { startPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
      startPOSPaymentOperation()

      // Prepare tokenization options
      const tokenizeOptions = {}
      if (showBillingAddress) {
        tokenizeOptions.billingContact = {
          addressLines: [billingAddress.addressLine1, billingAddress.addressLine2].filter(Boolean),
          city: billingAddress.locality,
          state: billingAddress.administrativeDistrictLevel1,
          postalCode: billingAddress.postalCode,
          country: billingAddress.country
        }
      }

      // Tokenize payment
      const result = await paymentForm.tokenize(tokenizeOptions)

      if (result.status === 'OK') {
        const paymentResponse = await processPayment(result.token)

        if (paymentResponse.success) {
          onSuccess({
            paymentId: paymentResponse.paymentId,
            paymentStatus: 'COMPLETED',
            paymentDetails: {
              token: result.token,
              amount: amount,
              currency: currency,
              transactionId: paymentResponse.transactionId
            }
          })
        } else {
          throw new Error(paymentResponse.error || 'Payment processing failed')
        }
      } else {
        const errorMsg = result.errors?.[0]?.message || 'Payment tokenization failed'
        setErrorMessage(errorMsg)
        onError(new Error(errorMsg))
      }
    } catch (error) {
      console.error('❌ Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)

      try {
        const { endPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
        endPOSPaymentOperation()
      } catch (protectionError) {
        console.warn('Error ending POS payment protection:', protectionError)
      }
    }
  }, [paymentForm, isProcessing, showBillingAddress, billingAddress, amount, currency, onSuccess, onError])

  // Process payment using Square API
  const processPayment = async (token) => {
    console.log('🔄 Processing payment with token...')

    try {
      const response = await fetch('/api/square/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceId: token,
          amountMoney: {
            amount: Math.round(amount * 100), // Convert to cents
            currency: currency
          },
          orderDetails: orderDetails
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Payment processing failed')
      }

      return result
    } catch (error) {
      console.error('❌ Payment processing error:', error)
      throw error
    }
  }



  return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>{errorMessage}</div>
            <button
              onClick={() => {
                setErrorMessage('')
                setIsLoading(true)
                if (paymentForm) {
                  try {
                    paymentForm.destroy()
                  } catch (error) {
                    console.warn('Error destroying form:', error)
                  }
                }
                setPaymentForm(null)
                setTimeout(() => {
                  if (mountedRef.current) {
                    initializeSquareForm()
                  }
                }, 100)
              }}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className={styles.cardFormContainer}>
        {/* Square payment form container */}
        <div
          ref={containerRef}
          className={styles.cardForm}
          style={{
            minHeight: '60px',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            padding: '16px',
            background: 'white'
          }}
        >
          {isLoading && (
            <div className={styles.cardFormPlaceholder}>
              <div className={styles.loadingSpinner}></div>
              <p>Initializing secure payment form...</p>
            </div>
          )}

          {!isLoading && !paymentForm && squareSDKLoaded && (
            <div className={styles.cardFormPlaceholder}>
              <p>Payment form ready to initialize...</p>
            </div>
          )}
        </div>

        {/* Billing Address Form for Sandbox AVS Verification */}
        {showBillingAddress && !isLoading && (
          <div className={styles.billingAddressForm}>
            <h5>Billing Address (Required for Test Environment)</h5>
            <div className={styles.addressGrid}>
              <div className={styles.addressField}>
                <label>Address Line 1</label>
                <input
                  type="text"
                  value={billingAddress.addressLine1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine1: e.target.value}))}
                  placeholder="1455 Market St"
                />
              </div>
              <div className={styles.addressField}>
                <label>Address Line 2</label>
                <input
                  type="text"
                  value={billingAddress.addressLine2}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine2: e.target.value}))}
                  placeholder="Suite 600"
                />
              </div>
              <div className={styles.addressField}>
                <label>City</label>
                <input
                  type="text"
                  value={billingAddress.locality}
                  onChange={(e) => setBillingAddress(prev => ({...prev, locality: e.target.value}))}
                  placeholder="San Francisco"
                />
              </div>
              <div className={styles.addressField}>
                <label>State</label>
                <input
                  type="text"
                  value={billingAddress.administrativeDistrictLevel1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, administrativeDistrictLevel1: e.target.value}))}
                  placeholder="CA"
                />
              </div>
              <div className={styles.addressField}>
                <label>ZIP Code</label>
                <input
                  type="text"
                  value={billingAddress.postalCode}
                  onChange={(e) => setBillingAddress(prev => ({...prev, postalCode: e.target.value}))}
                  placeholder="94103"
                />
              </div>
              <div className={styles.addressField}>
                <label>Country</label>
                <select
                  value={billingAddress.country}
                  onChange={(e) => setBillingAddress(prev => ({...prev, country: e.target.value}))}
                >
                  <option value="US">United States</option>
                  <option value="AU">Australia</option>
                  <option value="CA">Canada</option>
                  <option value="GB">United Kingdom</option>
                </select>
              </div>
            </div>
            <div className={styles.addressNote}>
              <p><strong>Note:</strong> This billing address is required for Square's sandbox environment to pass Address Verification System (AVS) checks. Use the pre-filled test address or enter a valid address.</p>
            </div>
          </div>
        )}
      </div>

      <div className={styles.paymentActions}>
        <button
          onClick={handlePayment}
          disabled={
            isProcessing ||
            !paymentForm ||
            !squareSDKLoaded ||
            isLoading ||
            !mountedRef.current
          }
          className={styles.processPaymentButton}
        >
          {isProcessing ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Processing...
            </>
          ) : isLoading ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Initializing...
            </>
          ) : !squareSDKLoaded ? (
            'Loading Payment System...'
          ) : !paymentForm ? (
            'Payment Form Not Ready'
          ) : (
            `Charge $${parseFloat(amount || 0).toFixed(2)}`
          )}
        </button>
      </div>

      <div className={styles.paymentSecurity}>
        <div className={styles.securityBadges}>
          <span className={styles.securityBadge}>🔒 SSL Encrypted</span>
          <span className={styles.securityBadge}>✅ PCI Compliant</span>
          <span className={styles.securityBadge}>🛡️ Square Secure</span>
        </div>
        <p className={styles.securityText}>
          Your payment information is processed securely by Square and never stored on our servers.
        </p>
      </div>
    </div>
  )
}
